[package]
name = "candle-bert-time-series"
version = "0.1.0"
edition = "2021"

[dependencies]
candle-core = { version = "0.9.1", features = [] }
candle-nn = { version = "0.9.1", features = [] }
candle-transformers = { version = "0.9.1", features = [] }
anyhow = "1"
tokio ={ version = "1", features = ["full"] }
serde = { version = "1", features = ["derive"] }
serde_json = "1"

# Data processing (for dataset creation and analysis)
polars = { version = "0.49.1", features = ["lazy", "csv", "temporal", "parquet"] }
chrono = "0.4"
walkdir = "2.3"
rayon = "1.10"
num_cpus = "1.16"
tempfile = "3.20"
rand = "0.8"
rand_chacha = "0.3"
plotters = "0.3"

hftbacktest = "0.8.1"

ordered-float = "5.0.0"
tracing = "0.1"

# HTTP client for data downloading
reqwest = { version = "0.11", features = ["json"] }
