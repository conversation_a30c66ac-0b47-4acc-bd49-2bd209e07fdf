//! Test hftbacktest with downloaded Bybit orderbook data
//!
//! This example tests the hftbacktest functionality using the real orderbook data
//! downloaded from Bybit to ensure everything works correctly before deployment.
//!
//! Usage:
//!   cargo run --example test_hftbacktest_bybit

use polars::prelude::*;
use std::collections::BTreeMap;
use std::path::Path;
use anyhow::{anyhow, Result};
use hftbacktest::prelude::*;

/// Load the parquet file and inspect its structure
fn inspect_orderbook_data(file_path: &str) -> Result<()> {
    println!("🔍 Inspecting orderbook data structure...");
    
    if !Path::new(file_path).exists() {
        return Err(anyhow!("File does not exist: {}", file_path));
    }

    let df = LazyFrame::scan_parquet(file_path, Default::default())?
        .limit(10) // Just look at first 10 rows
        .collect()?;

    println!("📊 Data structure:");
    println!("Columns: {:?}", df.get_column_names());
    println!("Shape: {:?}", df.shape());
    println!("Data types: {:?}", df.dtypes());
    
    println!("\n📋 Sample data:");
    println!("{}", df);

    // Get full statistics
    let full_df = LazyFrame::scan_parquet(file_path, Default::default())?
        .collect()?;
    
    println!("\n📈 Data statistics:");
    println!("Total events: {}", full_df.height());
    
    // Count event types
    if let Ok(event_counts) = full_df
        .clone()
        .lazy()
        .group_by([col("event_type")])
        .agg([len().alias("count")])
        .collect()
    {
        println!("Event type distribution:");
        println!("{}", event_counts);
    }

    // Time range
    if let Ok(time_stats) = full_df
        .clone()
        .lazy()
        .select([
            col("timestamp").min().alias("min_time"),
            col("timestamp").max().alias("max_time"),
        ])
        .collect()
    {
        println!("\nTime range:");
        println!("{}", time_stats);
    }

    // Price range
    if let Ok(price_stats) = full_df
        .clone()
        .lazy()
        .select([
            col("price").min().alias("min_price"),
            col("price").max().alias("max_price"),
            col("price").mean().alias("avg_price"),
        ])
        .collect()
    {
        println!("\nPrice statistics:");
        println!("{}", price_stats);
    }

    Ok(())
}

/// Load hftbacktest-compatible data directly (no conversion needed)
fn load_hftbacktest_data(file_path: &str) -> Result<Vec<Event>> {
    println!("📂 Loading hftbacktest-compatible data...");

    let df = LazyFrame::scan_parquet(file_path, Default::default())?
        .collect()?;

    let mut events = Vec::new();

    // Get columns in hftbacktest Event format
    let ev_flags = df.column("ev")?.u64()?;
    let exch_timestamps = df.column("exch_ts")?.i64()?;
    let local_timestamps = df.column("local_ts")?.i64()?;
    let prices = df.column("px")?.f64()?;
    let quantities = df.column("qty")?.f64()?;
    let order_ids = df.column("order_id")?.u64()?;
    let ivals = df.column("ival")?.i64()?;
    let fvals = df.column("fval")?.f64()?;

    println!("Loading {} events directly...", df.height());

    for i in 0..df.height() {
        events.push(Event {
            ev: ev_flags.get(i).unwrap_or(0),
            exch_ts: exch_timestamps.get(i).unwrap_or(0),
            local_ts: local_timestamps.get(i).unwrap_or(0),
            px: prices.get(i).unwrap_or(0.0),
            qty: quantities.get(i).unwrap_or(0.0),
            order_id: order_ids.get(i).unwrap_or(0),
            ival: ivals.get(i).unwrap_or(0),
            fval: fvals.get(i).unwrap_or(0.0),
        });
    }

    println!("✅ Loaded {} events directly", events.len());
    Ok(events)
}

/// Simple market making strategy for testing
struct SimpleMarketMaker {
    position: f64,
    spread_ticks: i64,
    order_qty: f64,
    max_position: f64,
}

impl SimpleMarketMaker {
    fn new() -> Self {
        Self {
            position: 0.0,
            spread_ticks: 10, // 10 tick spread
            order_qty: 0.1,   // 0.1 BTC orders
            max_position: 1.0, // Max 1 BTC position
        }
    }

    fn on_depth_update(&mut self, hbt: &mut HashMapMarketDepth, _timestamp: i64) -> Result<()> {
        // Get best bid/ask
        let best_bid_tick = hbt.best_bid_tick();
        let best_ask_tick = hbt.best_ask_tick();

        if best_bid_tick == INVALID_MIN || best_ask_tick == INVALID_MAX {
            return Ok(()); // No valid market
        }

        // For this simple test, we'll just track the market state
        // In a real strategy, you would place orders here

        Ok(())
    }

    fn on_trade(&mut self, trade: &Event) {
        // Update position based on trade
        if trade.px > 0.0 {
            self.position += trade.qty;
        } else {
            self.position -= trade.qty;
        }

        println!("Trade executed: price={}, qty={}, new_position={}",
                 trade.px, trade.qty, self.position);
    }
}

/// Run a simple backtest with the loaded data
fn run_simple_backtest(events: Vec<Event>) -> Result<()> {
    println!("🚀 Running simple backtest...");

    // Create market depth with tick size and lot size
    let tick_size = 0.01;
    let lot_size = 0.001;
    let mut hbt = HashMapMarketDepth::new(tick_size, lot_size);
    let mut strategy = SimpleMarketMaker::new();

    let mut trade_count = 0;
    let mut last_print_time = 0i64;
    let print_interval = 60_000_000_000i64; // Print every minute (nanoseconds)

    println!("Processing {} events...", events.len());

    for (i, event) in events.iter().enumerate() {
        // Apply the event to market depth manually
        if event.is(LOCAL_BID_DEPTH_EVENT) {
            // Bid side update - px is already in ticks, convert to price
            let price = event.px * tick_size;
            hbt.update_bid_depth(price, event.qty, event.local_ts);

            // Update strategy on depth changes
            if let Err(e) = strategy.on_depth_update(&mut hbt, event.local_ts) {
                println!("Strategy error: {}", e);
            }
        } else if event.is(LOCAL_ASK_DEPTH_EVENT) {
            // Ask side update - px is negative ticks for asks, convert to positive price
            let price = (-event.px) * tick_size;
            hbt.update_ask_depth(price, event.qty, event.local_ts);

            // Update strategy on depth changes
            if let Err(e) = strategy.on_depth_update(&mut hbt, event.local_ts) {
                println!("Strategy error: {}", e);
            }
        } else if event.is(LOCAL_BUY_TRADE_EVENT) || event.is(LOCAL_SELL_TRADE_EVENT) {
            strategy.on_trade(event);
            trade_count += 1;
        }

        // Print progress periodically
        if event.local_ts - last_print_time > print_interval {
            let best_bid = hbt.best_bid_tick();
            let best_ask = hbt.best_ask_tick();
            println!("Progress: {}/{} events, Time: {}, Best: {}/{}",
                     i + 1, events.len(), event.local_ts, best_bid, best_ask);
            last_print_time = event.local_ts;
        }

        // Break early for testing (process only first 100k events)
        if i >= 100_000 {
            println!("⚠️  Stopping early after 100k events for testing");
            break;
        }
    }

    println!("\n📊 Backtest Results:");
    println!("Total trades executed: {}", trade_count);
    println!("Final position: {}", strategy.position);

    // Get final market state
    let best_bid = hbt.best_bid_tick();
    let best_ask = hbt.best_ask_tick();
    println!("Final market: bid={}, ask={}", best_bid, best_ask);

    Ok(())
}

fn main() -> Result<()> {
    println!("🧪 Testing hftbacktest with Bybit orderbook data");
    println!("================================================");

    let data_file = "./output/2025-04-30_BTCUSDC_hft.parquet";

    // Step 1: Inspect the data structure
    inspect_orderbook_data(data_file)?;

    println!("\n{}", "=".repeat(50));

    // Step 2: Load data for hftbacktest
    let events = load_hftbacktest_data(data_file)?;

    println!("\n{}", "=".repeat(50));

    // Step 3: Run simple backtest
    run_simple_backtest(events)?;

    println!("\n✅ Test completed successfully!");
    println!("The hftbacktest integration is working with your Bybit data.");

    Ok(())
}
